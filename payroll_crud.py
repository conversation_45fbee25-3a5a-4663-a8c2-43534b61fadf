# PAYROLL MANAGEMENT SYSTEM
import sqlite3
import tkinter as tk
from tkinter import messagebox, ttk

# Database connection function
def connect_db():
    return sqlite3.connect('payroll_system.db')

# Create database tables
conn = connect_db()
c = conn.cursor()

c.execute('''CREATE TABLE IF NOT EXISTS Employees (
                id INTEGER PRIMARY KEY,
                name TEXT,
                age INTEGER,
                department TEXT)''')

c.execute('''CREATE TABLE IF NOT EXISTS Departments (
                id INTEGER PRIMARY KEY,
                name TEXT)''')

c.execute('''CREATE TABLE IF NOT EXISTS Salaries (
                id INTEGER PRIMARY KEY,
                employee_id INTEGER,
                base_salary REAL,
                bonus REAL,
                FOREIGN KEY(employee_id) REFERENCES Employees(id))''')

c.execute('''CREATE TABLE IF NOT EXISTS Payroll (
                id INTEGER PRIMARY KEY,
                employee_id INTEGER,
                salary_date TEXT,
                total_pay REAL,
                FOREIGN KEY(employee_id) REFERENCES Employees(id))''')

conn.commit()
conn.close()

# Execute database queries
def execute_query(query, params=()):
    conn = connect_db()
    c = conn.cursor()
    c.execute(query, params)
    conn.commit()
    conn.close()

# Fetch data from database
def fetch_all(query):
    conn = connect_db()
    c = conn.cursor()
    c.execute(query)
    data = c.fetchall()
    conn.close()
    return data

# Check if employee exists
def employee_exists(employee_id):
    conn = connect_db()
    c = conn.cursor()
    c.execute("SELECT COUNT(*) FROM Employees WHERE id = ?", (employee_id,))
    count = c.fetchone()[0]
    conn.close()
    return count > 0

# Add new employee
def add_employee():
    name = entry_name.get().strip()
    age = entry_age.get().strip()
    dept = entry_dept.get().strip()

    if not name or not age or not dept:
        messagebox.showwarning("Warning", "Fill all fields")
        return

    try:
        age_val = int(age)
        if age_val <= 0:
            raise ValueError
    except:
        messagebox.showwarning("Warning", "Invalid age")
        return

    execute_query("INSERT INTO Employees (name, age, department) VALUES (?, ?, ?)", (name, age_val, dept))
    messagebox.showinfo("Success", "Employee added")
    show_all_employees()
    clear_entries()

# Show all employees in tree view
def show_all_employees():
    tree.delete(*tree.get_children())
    employees = fetch_all("SELECT * FROM Employees")
    for emp in employees:
        tree.insert("", "end", values=emp)

# Delete selected employee
def delete_employee():
    selected = tree.selection()
    if selected:
        emp_id = tree.item(selected[0])['values'][0]
        if messagebox.askyesno("Delete", f"Delete Employee ID {emp_id}?"):
            execute_query("DELETE FROM Employees WHERE id=?", (emp_id,))
            messagebox.showinfo("Success", "Deleted")
            show_all_employees()
    else:
        messagebox.showwarning("Warning", "Select employee")

# Update selected employee
def update_employee():
    selected = tree.selection()
    if selected:
        emp_id = tree.item(selected[0])['values'][0]
        name = entry_name.get().strip()
        age = entry_age.get().strip()
        dept = entry_dept.get().strip()

        if not name or not age or not dept:
            messagebox.showwarning("Warning", "Fill all fields")
            return

        try:
            age_val = int(age)
            if age_val <= 0:
                raise ValueError
        except:
            messagebox.showwarning("Warning", "Invalid age")
            return

        execute_query("UPDATE Employees SET name=?, age=?, department=? WHERE id=?", (name, age_val, dept, emp_id))
        messagebox.showinfo("Success", "Updated")
        show_all_employees()
        clear_entries()
    else:
        messagebox.showwarning("Warning", "Select employee")

# Generate comprehensive report
def generate_report():
    try:
        total_employees = fetch_all("SELECT COUNT(*) FROM Employees")[0][0]
        total_departments = fetch_all("SELECT COUNT(*) FROM Departments")[0][0]
        total_salaries = fetch_all("SELECT COUNT(*) FROM Salaries")[0][0]
        total_payroll = fetch_all("SELECT COUNT(*) FROM Payroll")[0][0]

        salary_stats = fetch_all("SELECT AVG(base_salary + bonus), SUM(base_salary + bonus) FROM Salaries")
        avg_salary = salary_stats[0][0] if salary_stats[0][0] else 0
        total_salary_amount = salary_stats[0][1] if salary_stats[0][1] else 0

        payroll_stats = fetch_all("SELECT SUM(total_pay) FROM Payroll")
        total_payroll_amount = payroll_stats[0][0] if payroll_stats[0][0] else 0

        employees_no_salary = fetch_all("""SELECT COUNT(*) FROM Employees e
                                          WHERE e.id NOT IN (SELECT employee_id FROM Salaries)""")[0][0]

        employees_no_payroll = fetch_all("""SELECT COUNT(*) FROM Employees e
                                           WHERE e.id NOT IN (SELECT employee_id FROM Payroll)""")[0][0]

        report_window = tk.Toplevel(root)
        report_window.title("Comprehensive Payroll System Report")
        report_window.geometry("600x700")

        report_frame = tk.Frame(report_window)
        report_frame.pack(fill=tk.BOTH, expand=True)

        tk.Label(report_frame, text="COMPREHENSIVE PAYROLL SYSTEM REPORT").pack()

        overview_frame = tk.LabelFrame(report_frame, text="System Overview")
        overview_frame.pack(fill='x')

        tk.Label(overview_frame, text=f"Total Employees: {total_employees}").pack(anchor='w')
        tk.Label(overview_frame, text=f"Total Departments: {total_departments}").pack(anchor='w')
        tk.Label(overview_frame, text=f"Total Salary Records: {total_salaries}").pack(anchor='w')
        tk.Label(overview_frame, text=f"Total Payroll Records: {total_payroll}").pack(anchor='w')

        dept_frame = tk.LabelFrame(report_frame, text="Department Breakdown")
        dept_frame.pack(fill='x')

        departments = fetch_all("SELECT name FROM Departments ORDER BY name")
        for dept in departments:
            dept_count = fetch_all(f"SELECT COUNT(*) FROM Employees WHERE department = '{dept[0]}'")[0][0]
            tk.Label(dept_frame, text=f"  • {dept[0]}: {dept_count} employees").pack(anchor='w')

        salary_frame = tk.LabelFrame(report_frame, text="Salary Statistics")
        salary_frame.pack(fill='x')

        tk.Label(salary_frame, text=f"Average Salary (Base + Bonus): ${avg_salary:.2f}").pack(anchor='w')
        tk.Label(salary_frame, text=f"Total Salary Amount: ${total_salary_amount:.2f}").pack(anchor='w')
        tk.Label(salary_frame, text=f"Employees without Salary Records: {employees_no_salary}").pack(anchor='w')

        payroll_frame = tk.LabelFrame(report_frame, text="Payroll Statistics")
        payroll_frame.pack(fill='x')

        tk.Label(payroll_frame, text=f"Total Payroll Amount: ${total_payroll_amount:.2f}").pack(anchor='w')
        tk.Label(payroll_frame, text=f"Employees without Payroll Records: {employees_no_payroll}").pack(anchor='w')

        tk.Button(report_frame, text="Close", command=report_window.destroy).pack()

    except Exception as e:
        messagebox.showerror("Error", f"Failed to generate report: {str(e)}")

# ============================================================================
# DEPARTMENT MANAGEMENT FUNCTIONS
# ============================================================================
# Add new department
def add_department():
    name = dept_entry_name.get().strip()
    if not name:
        messagebox.showwarning("Warning", "Fill department name")
        return

    execute_query("INSERT INTO Departments (name) VALUES (?)", (name,))
    messagebox.showinfo("Success", "Department added")
    show_all_departments()
    clear_dept_entries()

# Show all departments in tree view
def show_all_departments():
    dept_tree.delete(*dept_tree.get_children())
    departments = fetch_all("SELECT * FROM Departments")
    for dept in departments:
        dept_tree.insert("", "end", values=dept)

# Delete selected department
def delete_department():
    selected = dept_tree.selection()
    if selected:
        dept_id = dept_tree.item(selected[0])['values'][0]
        if messagebox.askyesno("Delete", f"Delete Department ID {dept_id}?"):
            execute_query("DELETE FROM Departments WHERE id=?", (dept_id,))
            messagebox.showinfo("Success", "Deleted")
            show_all_departments()
    else:
        messagebox.showwarning("Warning", "Select department")

# Update selected department
def update_department():
    selected = dept_tree.selection()
    if selected:
        dept_id = dept_tree.item(selected[0])['values'][0]
        name = dept_entry_name.get().strip()
        if not name:
            messagebox.showwarning("Warning", "Fill department name")
            return

        execute_query("UPDATE Departments SET name=? WHERE id=?", (name, dept_id))
        messagebox.showinfo("Success", "Updated")
        show_all_departments()
        clear_dept_entries()
    else:
        messagebox.showwarning("Warning", "Select department")

# Clear department input field
def clear_dept_entries():
    dept_entry_name.delete(0, tk.END)

# Load selected department data into input field
def load_department_data(event=None):
    selected = dept_tree.selection()
    if selected:
        dept = dept_tree.item(selected[0])['values']
        clear_dept_entries()
        dept_entry_name.insert(0, dept[1])

# Add new salary record
def add_salary():
    emp_id = salary_entry_emp_id.get().strip()
    base_salary = salary_entry_base.get().strip()
    bonus = salary_entry_bonus.get().strip()

    if not emp_id or not base_salary:
        messagebox.showwarning("Warning", "Fill required fields")
        return

    try:
        emp_id_val = int(emp_id)
        base_salary_val = float(base_salary)
        bonus_val = float(bonus) if bonus else 0.0

        if emp_id_val <= 0 or base_salary_val < 0 or bonus_val < 0:
            raise ValueError
    except:
        messagebox.showwarning("Warning", "Invalid values")
        return

    if not employee_exists(emp_id_val):
        messagebox.showerror("Error", f"Employee ID {emp_id_val} does not exist!\nPlease create the employee first in the Employees tab.")
        return

    execute_query("INSERT INTO Salaries (employee_id, base_salary, bonus) VALUES (?, ?, ?)",
                  (emp_id_val, base_salary_val, bonus_val))
    messagebox.showinfo("Success", "Salary added")
    show_all_salaries()
    clear_salary_entries()

# Show all salary records in tree view
def show_all_salaries():
    salary_tree.delete(*salary_tree.get_children())
    salaries = fetch_all("""SELECT s.id, s.employee_id, e.name, s.base_salary, s.bonus
                           FROM Salaries s
                           LEFT JOIN Employees e ON s.employee_id = e.id""")
    for salary in salaries:
        salary_tree.insert("", "end", values=salary)

# Delete selected salary record
def delete_salary():
    selected = salary_tree.selection()
    if selected:
        salary_id = salary_tree.item(selected[0])['values'][0]
        if messagebox.askyesno("Delete", f"Delete Salary ID {salary_id}?"):
            execute_query("DELETE FROM Salaries WHERE id=?", (salary_id,))
            messagebox.showinfo("Success", "Deleted")
            show_all_salaries()
    else:
        messagebox.showwarning("Warning", "Select salary record")

# Update selected salary record
def update_salary():
    selected = salary_tree.selection()
    if selected:
        salary_id = salary_tree.item(selected[0])['values'][0]
        emp_id = salary_entry_emp_id.get().strip()
        base_salary = salary_entry_base.get().strip()
        bonus = salary_entry_bonus.get().strip()

        if not emp_id or not base_salary:
            messagebox.showwarning("Warning", "Fill required fields")
            return

        try:
            emp_id_val = int(emp_id)
            base_salary_val = float(base_salary)
            bonus_val = float(bonus) if bonus else 0.0

            if emp_id_val <= 0 or base_salary_val < 0 or bonus_val < 0:
                raise ValueError
        except:
            messagebox.showwarning("Warning", "Invalid values")
            return

        if not employee_exists(emp_id_val):
            messagebox.showerror("Error", f"Employee ID {emp_id_val} does not exist!\nPlease create the employee first in the Employees tab.")
            return

        execute_query("UPDATE Salaries SET employee_id=?, base_salary=?, bonus=? WHERE id=?",
                      (emp_id_val, base_salary_val, bonus_val, salary_id))
        messagebox.showinfo("Success", "Updated")
        show_all_salaries()
        clear_salary_entries()
    else:
        messagebox.showwarning("Warning", "Select salary record")

# Clear salary input fields
def clear_salary_entries():
    salary_entry_emp_id.delete(0, tk.END)
    salary_entry_base.delete(0, tk.END)
    salary_entry_bonus.delete(0, tk.END)

# Load selected salary data into input fields
def load_salary_data(event=None):
    selected = salary_tree.selection()
    if selected:
        salary = salary_tree.item(selected[0])['values']
        clear_salary_entries()
        salary_entry_emp_id.insert(0, salary[1])
        salary_entry_base.insert(0, salary[3])
        salary_entry_bonus.insert(0, salary[4])

# Add new payroll record
def add_payroll():
    emp_id = payroll_entry_emp_id.get().strip()
    salary_date = payroll_entry_date.get().strip()
    total_pay = payroll_entry_total.get().strip()

    if not emp_id or not salary_date or not total_pay:
        messagebox.showwarning("Warning", "Fill all fields")
        return

    try:
        emp_id_val = int(emp_id)
        total_pay_val = float(total_pay)

        if emp_id_val <= 0 or total_pay_val < 0:
            raise ValueError
    except:
        messagebox.showwarning("Warning", "Invalid values")
        return

    if not employee_exists(emp_id_val):
        messagebox.showerror("Error", f"Employee ID {emp_id_val} does not exist!\nPlease create the employee first in the Employees tab.")
        return

    execute_query("INSERT INTO Payroll (employee_id, salary_date, total_pay) VALUES (?, ?, ?)",
                  (emp_id_val, salary_date, total_pay_val))
    messagebox.showinfo("Success", "Payroll record added")
    show_all_payroll()
    clear_payroll_entries()

# Show all payroll records in tree view
def show_all_payroll():
    payroll_tree.delete(*payroll_tree.get_children())
    payroll_records = fetch_all("""SELECT p.id, p.employee_id, e.name, p.salary_date, p.total_pay
                                  FROM Payroll p
                                  LEFT JOIN Employees e ON p.employee_id = e.id""")
    for record in payroll_records:
        payroll_tree.insert("", "end", values=record)

# Delete selected payroll record
def delete_payroll():
    selected = payroll_tree.selection()
    if selected:
        payroll_id = payroll_tree.item(selected[0])['values'][0]
        if messagebox.askyesno("Delete", f"Delete Payroll ID {payroll_id}?"):
            execute_query("DELETE FROM Payroll WHERE id=?", (payroll_id,))
            messagebox.showinfo("Success", "Deleted")
            show_all_payroll()
    else:
        messagebox.showwarning("Warning", "Select payroll record")

# Update selected payroll record
def update_payroll():
    selected = payroll_tree.selection()
    if selected:
        payroll_id = payroll_tree.item(selected[0])['values'][0]
        emp_id = payroll_entry_emp_id.get().strip()
        salary_date = payroll_entry_date.get().strip()
        total_pay = payroll_entry_total.get().strip()

        if not emp_id or not salary_date or not total_pay:
            messagebox.showwarning("Warning", "Fill all fields")
            return

        try:
            emp_id_val = int(emp_id)
            total_pay_val = float(total_pay)

            if emp_id_val <= 0 or total_pay_val < 0:
                raise ValueError
        except:
            messagebox.showwarning("Warning", "Invalid values")
            return

        if not employee_exists(emp_id_val):
            messagebox.showerror("Error", f"Employee ID {emp_id_val} does not exist!\nPlease create the employee first in the Employees tab.")
            return

        execute_query("UPDATE Payroll SET employee_id=?, salary_date=?, total_pay=? WHERE id=?",
                      (emp_id_val, salary_date, total_pay_val, payroll_id))
        messagebox.showinfo("Success", "Updated")
        show_all_payroll()
        clear_payroll_entries()
    else:
        messagebox.showwarning("Warning", "Select payroll record")

# Clear payroll input fields
def clear_payroll_entries():
    payroll_entry_emp_id.delete(0, tk.END)
    payroll_entry_date.delete(0, tk.END)
    payroll_entry_total.delete(0, tk.END)

# Load selected payroll data into input fields
def load_payroll_data(event=None):
    selected = payroll_tree.selection()
    if selected:
        payroll = payroll_tree.item(selected[0])['values']
        clear_payroll_entries()
        payroll_entry_emp_id.insert(0, payroll[1])
        payroll_entry_date.insert(0, payroll[3])
        payroll_entry_total.insert(0, payroll[4])

# Clear employee input fields
def clear_entries():
    entry_name.delete(0, tk.END)
    entry_age.delete(0, tk.END)
    entry_dept.delete(0, tk.END)

# Load selected employee data into input fields
def load_employee_data(event=None):
    selected = tree.selection()
    if selected:
        emp = tree.item(selected[0])['values']
        clear_entries()
        entry_name.insert(0, emp[1])
        entry_age.insert(0, emp[2])
        entry_dept.insert(0, emp[3])

# GUI Setup
root = tk.Tk()
root.title("Payroll Management System")
root.geometry("1200x800")

main_frame = tk.Frame(root)
main_frame.pack(fill=tk.BOTH, expand=True)

title_label = tk.Label(main_frame, text="Payroll Management System")
title_label.pack()

notebook = ttk.Notebook(main_frame)
notebook.pack(fill=tk.BOTH, expand=True)

# Employees Tab
emp_tab = tk.Frame(notebook)
notebook.add(emp_tab, text="Employees")

emp_left_frame = tk.Frame(emp_tab)
emp_left_frame.pack(side=tk.LEFT, fill=tk.Y)

emp_input_frame = tk.LabelFrame(emp_left_frame, text="Employee Details")
emp_input_frame.pack(fill=tk.X)

tk.Label(emp_input_frame, text="Name:").pack(anchor='w')
entry_name = tk.Entry(emp_input_frame)
entry_name.pack()

tk.Label(emp_input_frame, text="Age:").pack(anchor='w')
entry_age = tk.Entry(emp_input_frame)
entry_age.pack()

tk.Label(emp_input_frame, text="Department:").pack(anchor='w')
entry_dept = tk.Entry(emp_input_frame)
entry_dept.pack()

emp_button_frame = tk.LabelFrame(emp_left_frame, text="Actions")
emp_button_frame.pack(fill=tk.X)

tk.Button(emp_button_frame, text="Add Employee", command=add_employee).pack()
tk.Button(emp_button_frame, text="Update Employee", command=update_employee).pack()
tk.Button(emp_button_frame, text="Delete Employee", command=delete_employee).pack()
tk.Button(emp_button_frame, text="Clear Fields", command=clear_entries).pack()
tk.Button(emp_button_frame, text="Generate Report", command=generate_report).pack()

emp_right_frame = tk.Frame(emp_tab)
emp_right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

emp_table_frame = tk.LabelFrame(emp_right_frame, text="Employee Records")
emp_table_frame.pack(fill=tk.BOTH, expand=True)

tree_columns = ("ID", "Name", "Age", "Department")
tree = ttk.Treeview(emp_table_frame, columns=tree_columns, show="headings", selectmode="browse")
for col in tree_columns:
    tree.heading(col, text=col)
    tree.column(col, minwidth=0, width=150, stretch=tk.NO)
tree.pack(fill=tk.BOTH, expand=True)

tree.bind('<<TreeviewSelect>>', load_employee_data)

emp_scrollbar = ttk.Scrollbar(emp_table_frame, orient=tk.VERTICAL, command=tree.yview)
emp_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
tree.configure(yscrollcommand=emp_scrollbar.set)

show_all_employees()

# Departments Tab
dept_tab = tk.Frame(notebook)
notebook.add(dept_tab, text="Departments")

dept_left_frame = tk.Frame(dept_tab)
dept_left_frame.pack(side=tk.LEFT, fill=tk.Y)

dept_input_frame = tk.LabelFrame(dept_left_frame, text="Department Details")
dept_input_frame.pack(fill=tk.X)

tk.Label(dept_input_frame, text="Department Name:").pack(anchor='w')
dept_entry_name = tk.Entry(dept_input_frame)
dept_entry_name.pack()

dept_button_frame = tk.LabelFrame(dept_left_frame, text="Actions")
dept_button_frame.pack(fill=tk.X)

tk.Button(dept_button_frame, text="Add Department", command=add_department).pack()
tk.Button(dept_button_frame, text="Update Department", command=update_department).pack()
tk.Button(dept_button_frame, text="Delete Department", command=delete_department).pack()
tk.Button(dept_button_frame, text="Clear Fields", command=clear_dept_entries).pack()

dept_right_frame = tk.Frame(dept_tab)
dept_right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

dept_table_frame = tk.LabelFrame(dept_right_frame, text="Department Records")
dept_table_frame.pack(fill=tk.BOTH, expand=True)

dept_tree_columns = ("ID", "Department Name")
dept_tree = ttk.Treeview(dept_table_frame, columns=dept_tree_columns, show="headings", selectmode="browse")
for col in dept_tree_columns:
    dept_tree.heading(col, text=col)
    dept_tree.column(col, minwidth=0, width=200, stretch=tk.NO)
dept_tree.pack(fill=tk.BOTH, expand=True)

dept_tree.bind('<<TreeviewSelect>>', load_department_data)

dept_scrollbar = ttk.Scrollbar(dept_table_frame, orient=tk.VERTICAL, command=dept_tree.yview)
dept_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
dept_tree.configure(yscrollcommand=dept_scrollbar.set)

show_all_departments()

# Salaries Tab
salary_tab = tk.Frame(notebook)
notebook.add(salary_tab, text="Salaries")

salary_left_frame = tk.Frame(salary_tab)
salary_left_frame.pack(side=tk.LEFT, fill=tk.Y)

salary_input_frame = tk.LabelFrame(salary_left_frame, text="Salary Details")
salary_input_frame.pack(fill=tk.X)

tk.Label(salary_input_frame, text="Employee ID:").pack(anchor='w')
salary_entry_emp_id = tk.Entry(salary_input_frame)
salary_entry_emp_id.pack()

tk.Label(salary_input_frame, text="Base Salary:").pack(anchor='w')
salary_entry_base = tk.Entry(salary_input_frame)
salary_entry_base.pack()

tk.Label(salary_input_frame, text="Bonus:").pack(anchor='w')
salary_entry_bonus = tk.Entry(salary_input_frame)
salary_entry_bonus.pack()

salary_button_frame = tk.LabelFrame(salary_left_frame, text="Actions")
salary_button_frame.pack(fill=tk.X)

tk.Button(salary_button_frame, text="Add Salary", command=add_salary).pack()
tk.Button(salary_button_frame, text="Update Salary", command=update_salary).pack()
tk.Button(salary_button_frame, text="Delete Salary", command=delete_salary).pack()
tk.Button(salary_button_frame, text="Clear Fields", command=clear_salary_entries).pack()

salary_right_frame = tk.Frame(salary_tab)
salary_right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

salary_table_frame = tk.LabelFrame(salary_right_frame, text="Salary Records")
salary_table_frame.pack(fill=tk.BOTH, expand=True)

salary_tree_columns = ("ID", "Employee ID", "Employee Name", "Base Salary", "Bonus")
salary_tree = ttk.Treeview(salary_table_frame, columns=salary_tree_columns, show="headings", selectmode="browse")
for col in salary_tree_columns:
    salary_tree.heading(col, text=col)
    salary_tree.column(col, minwidth=0, width=120, stretch=tk.NO)
salary_tree.pack(fill=tk.BOTH, expand=True)

salary_tree.bind('<<TreeviewSelect>>', load_salary_data)

salary_scrollbar = ttk.Scrollbar(salary_table_frame, orient=tk.VERTICAL, command=salary_tree.yview)
salary_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
salary_tree.configure(yscrollcommand=salary_scrollbar.set)

show_all_salaries()

# Payroll Tab
payroll_tab = tk.Frame(notebook)
notebook.add(payroll_tab, text="Payroll")

payroll_left_frame = tk.Frame(payroll_tab)
payroll_left_frame.pack(side=tk.LEFT, fill=tk.Y)

payroll_input_frame = tk.LabelFrame(payroll_left_frame, text="Payroll Details")
payroll_input_frame.pack(fill=tk.X)

tk.Label(payroll_input_frame, text="Employee ID:").pack(anchor='w')
payroll_entry_emp_id = tk.Entry(payroll_input_frame)
payroll_entry_emp_id.pack()

tk.Label(payroll_input_frame, text="Salary Date (YYYY-MM-DD):").pack(anchor='w')
payroll_entry_date = tk.Entry(payroll_input_frame)
payroll_entry_date.pack()

tk.Label(payroll_input_frame, text="Total Pay:").pack(anchor='w')
payroll_entry_total = tk.Entry(payroll_input_frame)
payroll_entry_total.pack()

payroll_button_frame = tk.LabelFrame(payroll_left_frame, text="Actions")
payroll_button_frame.pack(fill=tk.X)

tk.Button(payroll_button_frame, text="Add Payroll", command=add_payroll).pack()
tk.Button(payroll_button_frame, text="Update Payroll", command=update_payroll).pack()
tk.Button(payroll_button_frame, text="Delete Payroll", command=delete_payroll).pack()
tk.Button(payroll_button_frame, text="Clear Fields", command=clear_payroll_entries).pack()

payroll_right_frame = tk.Frame(payroll_tab)
payroll_right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

payroll_table_frame = tk.LabelFrame(payroll_right_frame, text="Payroll Records")
payroll_table_frame.pack(fill=tk.BOTH, expand=True)

payroll_tree_columns = ("ID", "Employee ID", "Employee Name", "Salary Date", "Total Pay")
payroll_tree = ttk.Treeview(payroll_table_frame, columns=payroll_tree_columns, show="headings", selectmode="browse")
for col in payroll_tree_columns:
    payroll_tree.heading(col, text=col)
    payroll_tree.column(col, minwidth=0, width=120, stretch=tk.NO)
payroll_tree.pack(fill=tk.BOTH, expand=True)

payroll_tree.bind('<<TreeviewSelect>>', load_payroll_data)

payroll_scrollbar = ttk.Scrollbar(payroll_table_frame, orient=tk.VERTICAL, command=payroll_tree.yview)
payroll_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
payroll_tree.configure(yscrollcommand=payroll_scrollbar.set)

show_all_payroll()

root.mainloop()
