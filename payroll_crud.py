# PAYROLL SYSTEM - SUPER SHORT VERSION
import sqlite3, tkinter as tk
from tkinter import messagebox, ttk

# Database helper
def db(query, params=(), get=False):
    with sqlite3.connect('payroll_system.db') as conn:
        return conn.execute(query, params).fetchall() if get else conn.execute(query, params)

# Create tables
[db(t) for t in [
    "CREATE TABLE IF NOT EXISTS Employees (id INTEGER PRIMARY KEY, name TEXT, age INTEGER, department TEXT)",
    "CREATE TABLE IF NOT EXISTS Departments (id INTEGER PRIMARY KEY, name TEXT)",
    "CREATE TABLE IF NOT EXISTS Salaries (id INTEGER PRIMARY KEY, employee_id INTEGER, base_salary REAL, bonus REAL)",
    "CREATE TABLE IF NOT EXISTS Payroll (id INTEGER PRIMARY KEY, employee_id INTEGER, salary_date TEXT, total_pay REAL)"
]]

# Helper functions
exists = lambda table, id: bool(db(f"SELECT 1 FROM {table} WHERE id=?", (id,), True))
validate = lambda vals, types: [t(v.strip()) for v, t in zip(vals, types)] if all(vals) else None

# CRUD Operations - Super Short
def crud(table, action, data=None, condition=None):
    queries = {
        'add': f"INSERT INTO {table} VALUES (NULL, {', '.join(['?' for _ in data])})",
        'get': f"SELECT * FROM {table}",
        'del': f"DELETE FROM {table} WHERE id=?",
        'upd': f"UPDATE {table} SET {', '.join([f'{k}=?' for k in data.keys()])} WHERE id=?"
    }
    if action in ['add', 'del', 'upd']:
        db(queries[action], data if action == 'add' else list(data.values()) + [condition] if action == 'upd' else [condition])
        messagebox.showinfo("Success", f"{action.title()}ed!")
    return db(queries['get'], get=True) if action == 'get' else None

# Placeholder functions - will be defined after GUI setup
def add_employee(): pass
def delete_employee(): pass
def update_employee(): pass
def show_all_employees(): pass

# Generate report - Super Short
def generate_report():
    try:
        stats = [db(f"SELECT COUNT(*) FROM {t}", get=True)[0][0] for t in ['Employees', 'Departments', 'Salaries', 'Payroll']]
        salary_stats = db("SELECT AVG(base_salary + bonus), SUM(base_salary + bonus) FROM Salaries", get=True)[0]
        payroll_amount = db("SELECT SUM(total_pay) FROM Payroll", get=True)[0][0] or 0

        rw = tk.Toplevel(root)
        rw.title("Payroll Report")
        rw.geometry("500x400")

        data = [
            ("System Overview", [f"Employees: {stats[0]}", f"Departments: {stats[1]}", f"Salaries: {stats[2]}", f"Payroll: {stats[3]}"]),
            ("Salary Stats", [f"Avg: ${salary_stats[0] or 0:.2f}", f"Total: ${salary_stats[1] or 0:.2f}"]),
            ("Payroll Stats", [f"Total: ${payroll_amount:.2f}"])
        ]

        for title, items in data:
            frame = tk.LabelFrame(rw, text=title)
            frame.pack(fill='x', padx=10, pady=5)
            for item in items:
                tk.Label(frame, text=item).pack(anchor='w')

        tk.Button(rw, text="Close", command=rw.destroy).pack(pady=10)
    except Exception as e:
        messagebox.showerror("Error", f"Report failed: {e}")

# More placeholder functions
def add_department(): pass
def delete_department(): pass
def update_department(): pass
def show_all_departments(): pass
def clear_dept_entries(): pass
def load_department_data(_=None): pass
def add_salary(): pass
def delete_salary(): pass
def update_salary(): pass
def show_all_salaries(): pass
def clear_salary_entries(): pass
def load_salary_data(_=None): pass
def add_payroll(): pass
def delete_payroll(): pass
def update_payroll(): pass
def show_all_payroll(): pass

# Placeholder functions - will be defined after GUI setup
def clear_payroll_entries(): pass
def clear_entries(): pass
def load_payroll_data(_=None): pass
def load_employee_data(_=None): pass

# GUI Setup - Super Short
root = tk.Tk()
root.title("Payroll Management System")
root.geometry("1200x800")

def create_tab(parent, name, fields, buttons, cols, tree_var, load_func):
    tab = tk.Frame(parent)
    parent.add(tab, name)

    # Left side - inputs and buttons
    left = tk.Frame(tab)
    left.pack(side=tk.LEFT, fill=tk.Y)

    # Input fields
    input_frame = tk.LabelFrame(left, text=f"{name} Details")
    input_frame.pack(fill=tk.X)
    entries = []
    for field in fields:
        tk.Label(input_frame, text=f"{field}:").pack(anchor='w')
        entry = tk.Entry(input_frame)
        entry.pack()
        entries.append(entry)

    # Buttons
    btn_frame = tk.LabelFrame(left, text="Actions")
    btn_frame.pack(fill=tk.X)
    for btn_text, cmd in buttons:
        tk.Button(btn_frame, text=btn_text, command=cmd).pack()

    # Right side - tree
    right = tk.Frame(tab)
    right.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
    table_frame = tk.LabelFrame(right, text=f"{name} Records")
    table_frame.pack(fill=tk.BOTH, expand=True)

    tree_widget = ttk.Treeview(table_frame, columns=cols, show="headings")
    for col in cols:
        tree_widget.heading(col, text=col)
        tree_widget.column(col, width=120)
    tree_widget.pack(fill=tk.BOTH, expand=True)
    tree_widget.bind('<<TreeviewSelect>>', load_func)

    globals()[tree_var] = tree_widget
    return entries

# Create main window
main_frame = tk.Frame(root)
main_frame.pack(fill=tk.BOTH, expand=True)
tk.Label(main_frame, text="Payroll Management System").pack()

notebook = ttk.Notebook(main_frame)
notebook.pack(fill=tk.BOTH, expand=True)

# Create all tabs
entry_name, entry_age, entry_dept = create_tab(notebook, "Employees", ["Name", "Age", "Department"], [("Add", add_employee), ("Update", update_employee), ("Delete", delete_employee), ("Clear", clear_entries), ("Report", generate_report)], ("ID", "Name", "Age", "Department"), "tree", load_employee_data)

dept_entry_name, = create_tab(notebook, "Departments", ["Department Name"], [("Add", add_department), ("Update", update_department), ("Delete", delete_department), ("Clear", clear_dept_entries)], ("ID", "Department Name"), "dept_tree", load_department_data)

salary_entry_emp_id, salary_entry_base, salary_entry_bonus = create_tab(notebook, "Salaries", ["Employee ID", "Base Salary", "Bonus"], [("Add", add_salary), ("Update", update_salary), ("Delete", delete_salary), ("Clear", clear_salary_entries)], ("ID", "Employee ID", "Employee Name", "Base Salary", "Bonus"), "salary_tree", load_salary_data)

payroll_entry_emp_id, payroll_entry_date, payroll_entry_total = create_tab(notebook, "Payroll", ["Employee ID", "Salary Date", "Total Pay"], [("Add", add_payroll), ("Update", update_payroll), ("Delete", delete_payroll), ("Clear", clear_payroll_entries)], ("ID", "Employee ID", "Employee Name", "Salary Date", "Total Pay"), "payroll_tree", load_payroll_data)

# Define all actual functions after GUI setup - Super Short
add_employee = lambda: (lambda v: db("INSERT INTO Employees (name, age, department) VALUES (?, ?, ?)", v) or messagebox.showinfo("Success", "Added") or show_all_employees() or clear_entries() if v and v[1] > 0 else messagebox.showwarning("Warning", "Invalid data"))(validate([entry_name.get(), entry_age.get(), entry_dept.get()], [str, int, str]))

delete_employee = lambda: (lambda s: (lambda id: db("DELETE FROM Employees WHERE id=?", [id]) or messagebox.showinfo("Success", "Deleted") or show_all_employees())(tree.item(s[0])['values'][0]) if messagebox.askyesno("Delete", f"Delete Employee {tree.item(s[0])['values'][0]}?") else None if s else messagebox.showwarning("Warning", "Select employee"))(tree.selection())

update_employee = lambda: (lambda s, v: (lambda id: db("UPDATE Employees SET name=?, age=?, department=? WHERE id=?", v + [id]) or messagebox.showinfo("Success", "Updated") or show_all_employees() or clear_entries())(tree.item(s[0])['values'][0]) if v and v[1] > 0 else messagebox.showwarning("Warning", "Invalid data") if s else messagebox.showwarning("Warning", "Select employee"))(tree.selection(), validate([entry_name.get(), entry_age.get(), entry_dept.get()], [str, int, str]))

show_all_employees = lambda: [tree.delete(*tree.get_children())] + [tree.insert("", "end", values=emp) for emp in db("SELECT * FROM Employees", get=True)]

add_department = lambda: (lambda n: db("INSERT INTO Departments (name) VALUES (?)", [n]) or messagebox.showinfo("Success", "Added") or show_all_departments() or clear_dept_entries() if n else messagebox.showwarning("Warning", "Fill name"))(dept_entry_name.get().strip())

delete_department = lambda: (lambda s: (lambda id: db("DELETE FROM Departments WHERE id=?", [id]) or messagebox.showinfo("Success", "Deleted") or show_all_departments())(dept_tree.item(s[0])['values'][0]) if messagebox.askyesno("Delete", f"Delete Department {dept_tree.item(s[0])['values'][0]}?") else None if s else messagebox.showwarning("Warning", "Select department"))(dept_tree.selection())

update_department = lambda: (lambda s, n: (lambda id: db("UPDATE Departments SET name=? WHERE id=?", [n, id]) or messagebox.showinfo("Success", "Updated") or show_all_departments() or clear_dept_entries())(dept_tree.item(s[0])['values'][0]) if n else messagebox.showwarning("Warning", "Fill name") if s else messagebox.showwarning("Warning", "Select department"))(dept_tree.selection(), dept_entry_name.get().strip())

show_all_departments = lambda: [dept_tree.delete(*dept_tree.get_children())] + [dept_tree.insert("", "end", values=dept) for dept in db("SELECT * FROM Departments", get=True)]

add_salary = lambda: (lambda v: db("INSERT INTO Salaries (employee_id, base_salary, bonus) VALUES (?, ?, ?)", v) or messagebox.showinfo("Success", "Added") or show_all_salaries() or clear_salary_entries() if v and v[0] > 0 and v[1] >= 0 and v[2] >= 0 and exists('Employees', v[0]) else messagebox.showerror("Error", "Invalid data or employee doesn't exist"))(validate([salary_entry_emp_id.get(), salary_entry_base.get(), salary_entry_bonus.get() or "0"], [int, float, float]))

delete_salary = lambda: (lambda s: (lambda id: db("DELETE FROM Salaries WHERE id=?", [id]) or messagebox.showinfo("Success", "Deleted") or show_all_salaries())(salary_tree.item(s[0])['values'][0]) if messagebox.askyesno("Delete", f"Delete Salary {salary_tree.item(s[0])['values'][0]}?") else None if s else messagebox.showwarning("Warning", "Select salary"))(salary_tree.selection())

update_salary = lambda: (lambda s, v: (lambda id: db("UPDATE Salaries SET employee_id=?, base_salary=?, bonus=? WHERE id=?", v + [id]) or messagebox.showinfo("Success", "Updated") or show_all_salaries() or clear_salary_entries())(salary_tree.item(s[0])['values'][0]) if v and v[0] > 0 and v[1] >= 0 and v[2] >= 0 and exists('Employees', v[0]) else messagebox.showerror("Error", "Invalid data or employee doesn't exist") if s else messagebox.showwarning("Warning", "Select salary"))(salary_tree.selection(), validate([salary_entry_emp_id.get(), salary_entry_base.get(), salary_entry_bonus.get() or "0"], [int, float, float]))

show_all_salaries = lambda: [salary_tree.delete(*salary_tree.get_children())] + [salary_tree.insert("", "end", values=s) for s in db("SELECT s.id, s.employee_id, e.name, s.base_salary, s.bonus FROM Salaries s LEFT JOIN Employees e ON s.employee_id = e.id", get=True)]

add_payroll = lambda: (lambda v: db("INSERT INTO Payroll (employee_id, salary_date, total_pay) VALUES (?, ?, ?)", v) or messagebox.showinfo("Success", "Added") or show_all_payroll() or clear_payroll_entries() if v and all(v) and v[0] > 0 and v[2] >= 0 and exists('Employees', v[0]) else messagebox.showerror("Error", "Invalid data or employee doesn't exist"))(validate([payroll_entry_emp_id.get(), payroll_entry_date.get(), payroll_entry_total.get()], [int, str, float]))

delete_payroll = lambda: (lambda s: (lambda id: db("DELETE FROM Payroll WHERE id=?", [id]) or messagebox.showinfo("Success", "Deleted") or show_all_payroll())(payroll_tree.item(s[0])['values'][0]) if messagebox.askyesno("Delete", f"Delete Payroll {payroll_tree.item(s[0])['values'][0]}?") else None if s else messagebox.showwarning("Warning", "Select payroll"))(payroll_tree.selection())

update_payroll = lambda: (lambda s, v: (lambda id: db("UPDATE Payroll SET employee_id=?, salary_date=?, total_pay=? WHERE id=?", v + [id]) or messagebox.showinfo("Success", "Updated") or show_all_payroll() or clear_payroll_entries())(payroll_tree.item(s[0])['values'][0]) if v and all(v) and v[0] > 0 and v[2] >= 0 and exists('Employees', v[0]) else messagebox.showerror("Error", "Invalid data or employee doesn't exist") if s else messagebox.showwarning("Warning", "Select payroll"))(payroll_tree.selection(), validate([payroll_entry_emp_id.get(), payroll_entry_date.get(), payroll_entry_total.get()], [int, str, float]))

show_all_payroll = lambda: [payroll_tree.delete(*payroll_tree.get_children())] + [payroll_tree.insert("", "end", values=p) for p in db("SELECT p.id, p.employee_id, e.name, p.salary_date, p.total_pay FROM Payroll p LEFT JOIN Employees e ON p.employee_id = e.id", get=True)]

# Clear and load functions
clear_entries = lambda: [e.delete(0, tk.END) for e in [entry_name, entry_age, entry_dept]]
clear_dept_entries = lambda: dept_entry_name.delete(0, tk.END)
clear_salary_entries = lambda: [e.delete(0, tk.END) for e in [salary_entry_emp_id, salary_entry_base, salary_entry_bonus]]
clear_payroll_entries = lambda: [e.delete(0, tk.END) for e in [payroll_entry_emp_id, payroll_entry_date, payroll_entry_total]]

load_employee_data = lambda _=None: (clear_entries(), [e.insert(0, v) for e, v in zip([entry_name, entry_age, entry_dept], [tree.item(tree.selection()[0])['values'][i] for i in [1, 2, 3]])]) if tree.selection() else None
load_department_data = lambda _=None: (clear_dept_entries(), dept_entry_name.insert(0, dept_tree.item(dept_tree.selection()[0])['values'][1])) if dept_tree.selection() else None
load_salary_data = lambda _=None: (clear_salary_entries(), [e.insert(0, v) for e, v in zip([salary_entry_emp_id, salary_entry_base, salary_entry_bonus], [salary_tree.item(salary_tree.selection()[0])['values'][i] for i in [1, 3, 4]])]) if salary_tree.selection() else None
load_payroll_data = lambda _=None: (clear_payroll_entries(), [e.insert(0, v) for e, v in zip([payroll_entry_emp_id, payroll_entry_date, payroll_entry_total], [payroll_tree.item(payroll_tree.selection()[0])['values'][i] for i in [1, 3, 4]])]) if payroll_tree.selection() else None

# Initialize and run
show_all_employees(); show_all_departments(); show_all_salaries(); show_all_payroll()
root.mainloop()
